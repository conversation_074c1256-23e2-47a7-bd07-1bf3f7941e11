%%
% Copyright (C) 2019-2023 by <PERSON><PERSON><PERSON> <<EMAIL>>
%%
%% This file is part of the hitszthesis package project.
%% ---------------------------------------------------
%%
%% This file may be distributed and/or modified under the
%% conditions of the LaTeX Project Public License, either version 1.3a
%% of this license or (at your option) any later version.
%% The latest version of this license is in:
%%
%% http://www.latex-project.org/lppl.txt
%%
%% and version 1.3a or later is part of all distributions of LaTeX
%% version 2004/10/01 or later.
%%

\input docstrip

\askforoverwritefalse
%\askonceonly
\showprogress
\keepsilent

\usedir{tex/latex/hitszthesis}

\preamble

This is a generated file.

Copyright (C) 2019-2023 by <PERSON><PERSON><PERSON> <<EMAIL>>

This file may be distributed and/or modified under the
conditions of the LaTeX Project Public License, either version 1.3a
of this license or (at your option) any later version.
The latest version of this license is in:

http://www.latex-project.org/lppl.txt

and version 1.3a or later is part of all distributions of LaTeX
version 2004/10/01 or later.

To produce the documentation run the original source files ending with `.dtx'
through LaTeX.

\endpreamble

\declarepreamble\cfgpreamble

This is a generated file.

Copyright (C) 2019-2023 by Jingxuan Yang <<EMAIL>>

This file may be distributed and/or modified under the
conditions of the LaTeX Project Public License, either version 1.3a
of this license or (at your option) any later version.
The latest version of this license is in:

http://www.latex-project.org/lppl.txt

and version 1.3a or later is part of all distributions of LaTeX
version 2004/10/01 or later.

This is the configuration file of the hitszthesis package with LaTeX2e.

\endpreamble
\declarepreamble\istpreamble

This is a generated file.

Copyright (C) 2019-2023 by Jingxuan Yang <<EMAIL>>

This file may be distributed and/or modified under the
conditions of the LaTeX Project Public License, either version 1.3a
of this license or (at your option) any later version.
The latest version of this license is in:

http://www.latex-project.org/lppl.txt

and version 1.3a or later is part of all distributions of LaTeX
version 2004/10/01 or later.

This is the configuration file of the hitszthesis package with LaTeX2e.

\endpreamble

\generate{
	\file{\jobname.cls}{\from{\jobname.dtx}{cls}}
          \usepreamble\cfgpreamble
          \file{\jobname.cfg}{\from{\jobname.dtx}{cfg}}
          \usepreamble\istpreamble
          \file{\jobname.ist}{\from{\jobname.dtx}{ist}}
          \usepreamble\defaultpreamble\usepostamble\defaultpostamble
          \file{dtx-style.sty}{\from{\jobname.dtx}{dtx-style}}}

\ifToplevel{%
  \Msg{***********************************************************}
  \Msg{*}
  \Msg{* To finish the installation you have to move the following}
  \Msg{* files into a directory searched by TeX:}
  \Msg{*}
  \Msg{* The recommended directory is TEXMF/tex/latex/hitszthesis}
  \Msg{*}
  \Msg{* \space\space hitszthesis.cls}
  \Msg{* \space\space hitszthesis.cfg}
  \Msg{* \space\space hitszthesis.ist}
  \Msg{*}
  \Msg{* To produce the documentation run the files ending with}
  \Msg{* `.dtx' through LaTeX.}
  \Msg{*}
  \Msg{* Happy TeXing!}
  \Msg{***********************************************************}}

\endbatchfile
