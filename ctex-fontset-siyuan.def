\ProvidesExplFile{ctex-fontset-siyuan.def}
  {\ExplFileDate}{2.4.9}{\ExplFileDescription}
\tl_new:N \l__ctex_msyh_suffix_tl
\tl_set:Nn \l__ctex_msyh_suffix_tl { .ttc }
\file_if_exist:nF { C:/Windows/Fonts/msyh.ttc }
  {
    \file_if_exist:nF { msyh.ttc }
      { \tl_set:Nn \l__ctex_msyh_suffix_tl { .ttf } }
  }
\sys_if_engine_pdftex:TF
  {
    \ctex_zhmap_case:nnn
      {
        \ctex_punct_set:n { windows }
        \setCJKmainfont
          [ BoldFont = simhei.ttf , ItalicFont = simkai.ttf ] { simsun.ttc }
        \setCJKsansfont [ BoldFont = msyhbd\l__ctex_msyh_suffix_tl ] { msyh\l__ctex_msyh_suffix_tl }
        \setCJKfamilyfont { zhyahei }
          [ BoldFont = msyhbd\l__ctex_msyh_suffix_tl ] { msyh\l__ctex_msyh_suffix_tl }
        \ctex_punct_map_family:nn { \CJKsfdefault } { zhyahei }
        \ctex_punct_map_bfseries:nn { \CJKsfdefault , zhyahei } { zhyaheib }
        \setCJKmonofont { simfang.ttf }
        \setCJKfamilyfont { zhkai }  { simkai.ttf }
        \setCJKfamilyfont { zhfs }   { simfang.ttf }
        \setCJKfamilyfont { zhsong } { simsun.ttc }
        \setCJKfamilyfont { zhhei }  { simhei.ttf }
        \setCJKfamilyfont { zhli }   { simli.ttf }
        \setCJKfamilyfont { zhyou }  { simyou.ttf }
        \ctex_punct_map_family:nn { \CJKrmdefault } { zhsong }
        \ctex_punct_map_family:nn { \CJKttdefault } { zhfs }
        \ctex_punct_map_itshape:nn { \CJKrmdefault } { zhkai }
        \ctex_punct_map_bfseries:nn { \CJKrmdefault } { zhhei }
      }
      {
        \ctex_load_zhmap:nnnn { rm } { zhhei } { zhfs } { zhwindowsfonts }
        \ctex_punct_set:n { windows }
        \ctex_punct_map_family:nn { \CJKrmdefault } { zhsong }
        \ctex_punct_map_bfseries:nn { \CJKrmdefault } { zhhei }
        \ctex_punct_map_itshape:nn { \CJKrmdefault } { zhkai }
      }
      {
        \tl_set:Nn \CJKrmdefault { rm }
        \tl_set:Nn \CJKsfdefault { sf }
        \tl_set:Nn \CJKttdefault { tt }
      }
  }
  {
    \sys_if_engine_uptex:TF
      {
        \ctex_set_upfonts:nnnnnn
          {simsun.ttc} {simhei.ttf} {simkai.ttf}
          {msyh\l__ctex_msyh_suffix_tl} {msyhbd\l__ctex_msyh_suffix_tl}
          {simfang.ttf}
        \ctex_set_upfamily:nnn { zhsong } { upzhserif } {}
        \ctex_set_upfamily:nnn { zhhei } { upzhserifb } {}
        \ctex_set_upfamily:nnn { zhfs } { upzhmono} {}
        \ctex_set_upfamily:nnn { zhkai } { upzhserifit } {}
        \ctex_set_upfamily:nnn { zhyahei } { upzhsans } { upzhsansb }
        \ctex_set_upfamily:nnn { zhli } { upschrm } {}
        \ctex_set_upmap:nnn { upstsl } { simli.ttf } {}
        \ctex_set_upfamily:nnn { zhyou } { upschgt } {}
        \ctex_set_upmap:nnn { upstht } { simyou.ttf } {}
      }
      {
        \setCJKmainfont
          [ BoldFont = SimHei , ItalicFont = KaiTi ] { SourceHanSerifCN-Regular }
        \setCJKsansfont
          [ BoldFont = { *~Bold } ] { Microsoft~YaHei }
        \setCJKmonofont { FangSong }
        \setCJKfamilyfont { zhkai } { KaiTi }
        \setCJKfamilyfont { zhfs }  { FangSong }
        \setCJKfamilyfont { zhsong }  { SourceHanSerifCN-Regular }
        \setCJKfamilyfont { zhhei }   { SimHei }
        \setCJKfamilyfont { zhli }    { LiSu }
        \setCJKfamilyfont { zhyou }   { YouYuan }
        \setCJKfamilyfont { zhyahei }
          [ BoldFont = { *~Bold } ] { Microsoft~YaHei }
      }
  }
\NewDocumentCommand \songti   { } { \CJKfamily { zhsong } }
\NewDocumentCommand \heiti    { } { \CJKfamily { zhhei } }
\NewDocumentCommand \fangsong { } { \CJKfamily { zhfs } }
\NewDocumentCommand \kaishu   { } { \CJKfamily { zhkai } }
\NewDocumentCommand \lishu    { } { \CJKfamily { zhli } }
\NewDocumentCommand \youyuan  { } { \CJKfamily { zhyou } }
\NewDocumentCommand \yahei    { } { \CJKfamily { zhyahei } }
